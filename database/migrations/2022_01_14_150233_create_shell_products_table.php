<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShellProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shell_products', function (Blueprint $table) {
            $table->increments('id');
            $table->string('shell_url')->index()->comment('壳站url');
            $table->string('sku')->nullable()->comment('SKU');
            $table->string('name')->index()->comment('产品名称');
            $table->string('url')->nullable()->comment('产品链接');
            $table->string('attribute')->nullable()->comment('产品属性');
            $table->decimal('price',15)->default(0.00)->index()->comment('产品单价');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shell_products');
    }
}
