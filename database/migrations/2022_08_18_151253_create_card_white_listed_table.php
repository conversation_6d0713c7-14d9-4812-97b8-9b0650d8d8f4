<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateCardWhiteListedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('card_white_listed', function (Blueprint $table) {
            $table->increments('id');
            $table->string('card_number', 64)->unique()->comment('卡号加密');
            $table->string('card_mask', 16)->index()->comment('卡掩码');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('card_white_listed');
    }
}
