<?php

namespace App\Classes\Risk\Gateways;

use App\Classes\Pay\Log;
use App\Classes\Supports\Traits\HasHttpRequest;

class Seon
{
    use HasHttpRequest;

    public function fraud($order): bool
    {
        $payload = [
            'config'               => [
                'ip'                    => [
                    'include' => 'flags,history,id',
                    'version' => 'v1',
                ],
//                'email'                 => [
//                    'include' => 'flags,history,id',
//                    'version' => 'v2',
//                ],
//                'phone'                 => [
//                    'include' => 'flags,history,id',
//                    'version' => 'v1',
//                ],
                'ip_api'                => true,
//                'email_api'             => true,
//                'phone_api'             => true,
            ],
            'ip'                   => $order['ip'],
            'action_type'          => 'withdrawal',
            'transaction_id'       => $order['transaction_id'],
            'email'                => $order['email'],
            'phone_number'         => $order['phone_number'],
            'merchant_id'          => $order['merchant_id'],
            'user_fullname'        => $order['user_fullname'],
            'shipping_country'     => $order['shipping_country'],
            'shipping_city'        => $order['shipping_city'],
            'shipping_region'      => $order['shipping_region'],
            'shipping_zip'         => $order['shipping_zip'],
            'shipping_street'      => $order['shipping_street'],
            'shipping_phone'       => $order['shipping_phone'],
            'shipping_fullname'    => $order['shipping_fullname'],
            'billing_country'      => $order['billing_country'],
            'billing_city'         => $order['billing_city'],
            'billing_region'       => $order['billing_region'],
            'billing_zip'          => $order['billing_zip'],
            'billing_street'       => $order['billing_street'],
            'billing_phone'        => $order['billing_phone'],
            'payment_mode'         => 'card',
            'cvv_result'           => true,
            'transaction_type'     => 'purchase',
            'card_fullname'        => $order['card_fullname'],
            'card_bin'             => $order['card_bin'],
            'card_hash'            => $order['card_hash'],
            'card_expire'          => $order['card_expire'],
            'card_last'            => $order['card_last'],
            'transaction_amount'   => $order['transaction_amount'],
            'transaction_currency' => $order['transaction_currency']
         ];

        $header = [];

        $res = $this->getRequest('/SeonRestService/fraud-api/v2', $payload, $header);

        switch ($res['state'] ?? 'DECLINE') {
            case 'APPROVE':
            case 'REVIEW':
                return false;
            case 'DECLINE':
            default:
                return true;
        }
    }

    public function getRequest(string $url, array $data, array $header = [])
    {
        $baseHeader = [
            'Content-Type' => 'application/json',
            'X-API-KEY'    => config('seon.key'),
        ];

        $header = array_merge($header, $baseHeader);

        try {
            Log::info(json_encode($data));
            $res = $this->post(config('seon.hosts') . $url, [], ['json' => $data, 'headers' => $header]);
            Log::info(json_encode($res));
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return [];
        }

        if (!isset($res['success']) || $res['success'] !== true) {
            return [];
        }

        return $res['data'];
    }

}