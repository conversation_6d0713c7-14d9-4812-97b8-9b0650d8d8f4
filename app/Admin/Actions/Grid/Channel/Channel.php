<?php

namespace App\Admin\Actions\Grid\Channel;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\Filter;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Channel as ChannelModel;
use App\Admin\Repositories\Channel as ChannelRepositories;
use Illuminate\Support\Facades\Cache;

class Channel extends LazyRenderable
{
    public function grid(): Grid
    {
        return Grid::make(new ChannelRepositories(['channelPid']), function (Grid $grid) {
            $_this      = $this;
            $channelPid = $this->payload['id'];

            $grid->setResource("/channel_pids/{$channelPid}/channels");
            $grid->model()->where('channel_pid_id', '=', $channelPid);

            $grid->id->sortable();

            $grid->fixColumns(2, -3);

            $grid->column('channel', '账单标识');
            $grid->column('day_statistics', '卡种/日交易笔数/日交易金额')->display(function () {
                $key                = 'Transaction_' . MD5($this->channel) . '_Qty_And_Amount_' . date('Ym');  //缓存key
                $transactionChannel = Cache::has($key) ? Cache::get($key) : [];
                $statistics['*']    = ['day_qty' => 0, 'day_amount' => 0];
                $statisticsMsg      = '';
                if (count($transactionChannel)) {
                    if (isset($transactionChannel['day_qty'], $transactionChannel['day_amount'])) {
                        foreach ($transactionChannel['day_qty'] as $ccType => $value) {
                            $statistics[$ccType] = ['day_qty' => $value, 'day_amount' => $transactionChannel['day_amount'][$ccType]];
                        }
                    }
                }

                foreach ($statistics as $ccType => $data) {
                    $statisticsMsg .= $ccType . ' / ' . $data['day_qty'] . ' / ' . amount_format($data['day_amount']) . '<br>';
                }

                $this->day_statistics = $statistics;
                return $statisticsMsg;
            })->width('10%');

            $grid->column('month_statistics', '卡种/月交易笔数/月交易金额')->display(function () {
                $key                = 'Transaction_' . MD5($this->channel) . '_Qty_And_Amount_' . date('Ym');  //缓存key
                $transactionChannel = Cache::has($key) ? Cache::get($key) : [];
                $statistics['*']    = ['month_qty' => 0, 'month_amount' => 0];
                $statisticsMsg      = '';
                if (count($transactionChannel)) {
                    if (isset($transactionChannel['month_qty'], $transactionChannel['month_amount'])) {
                        foreach ($transactionChannel['month_qty'] as $ccType => $value) {
                            $statistics[$ccType] = ['month_qty' => $value, 'month_amount' => $transactionChannel['month_amount'][$ccType]];
                        }
                    }
                }

                foreach ($statistics as $ccType => $data) {
                    $statisticsMsg .= $ccType . ' / ' . $data['month_qty'] . ' / ' . amount_format($data['month_amount']) . '<br>';
                }

                $this->month_statistics = $statistics;
                return $statisticsMsg;
            })->width('10%');

            $grid->column('single_money_astrict','单卡/单笔限额(USD)')->width('10%')->display(function ($value) {
                if (empty($value)) return '无';
                return collect($value)->map(function ($item) {
                    $min = "Min ".($item['min_money_astrict'] ?? '(无)');
                    $max = "Max ".($item['max_money_astrict'] ?? '(无)');
                    return "{$item['cc_type']}: $min ~ $max";
                })->implode('<br>');
            });
            $grid->column('day_astrict', '卡种/日限额/日限笔')->display(function ($value) {
                if (!empty($value)) {
                    $astrict = '';
                    foreach ($value as $limit) {
                        $astrict .= $limit['cc_type'] . '&nbsp;/&nbsp;' . $limit['day_money_astrict'] . '&nbsp;/&nbsp;'  . $limit['day_number_astrict'] . '<br />';
                    }
                    return $astrict;
                } else {
                    return '无';
                }
            });
            $grid->column('month_astrict', '卡种/月限额/月限笔')->display(function ($value) {
                if (!empty($value)) {
                    $astrict = '';
                    foreach ($value as $limit) {
                        $astrict .= $limit['cc_type'] . '&nbsp;/&nbsp;' . $limit['month_money_astrict'] . '&nbsp;/&nbsp;'  . $limit['month_number_astrict'] . '<br />';
                    }
                    return $astrict;
                } else {
                    return '无';
                }
            });

            $grid->column('exceed_astrict', '超限状态')->display(function ($value) {
                $dayAstrict         = $this->day_astrict ?? [];
                $monthAstrict       = $this->month_astrict ?? [];
                $dayMoneyAstrict    = array_column($dayAstrict, 'day_money_astrict', 'cc_type');
                $dayNumberAstrict   = array_column($dayAstrict, 'day_number_astrict', 'cc_type');
                $monthMoneyAstrict  = array_column($monthAstrict, 'month_money_astrict', 'cc_type');
                $monthNumberAstrict = array_column($monthAstrict, 'month_number_astrict', 'cc_type');
                
                foreach ($this->day_statistics as $ccType => $statistic) {
                    if (isset($dayMoneyAstrict[$ccType]) && $dayMoneyAstrict[$ccType] != 0 && $statistic['day_amount'] != 0 && $dayMoneyAstrict[$ccType] <= $statistic['day_amount']) {
                        return '超限';
                    }

                    if (isset($dayNumberAstrict[$ccType]) && $dayNumberAstrict[$ccType] != 0 && $statistic['day_qty'] != 0 && $dayNumberAstrict[$ccType] <= $statistic['day_qty']) {
                        return '超限';
                    }
                }

                foreach ($this->month_statistics as $ccType => $statistic) {
                    if (isset($monthMoneyAstrict[$ccType]) && $monthMoneyAstrict[$ccType] != 0 && $statistic['month_amount'] != 0 && $monthMoneyAstrict[$ccType] <= $statistic['month_amount']) {
                        return '超限';
                    }

                    if (isset($monthNumberAstrict[$ccType]) && $monthNumberAstrict[$ccType] != 0 && $statistic['month_qty'] != 0 && $monthNumberAstrict[$ccType] <= $statistic['month_qty']) {
                        return '超限';
                    }
                }
                
                return '正常';
            });
            $grid->column('status', '状态')->display(function ($value) {
                return ChannelModel::$statusMap[$value];
            });
            $grid->column('charge_type', '计费模式')->display(function ($value) {
                return ChannelModel::$chargeTypeMap[$value];
            });
            $grid->column('charge_rate', '计费比例(%)');
            $grid->column('url', '网址');
            $grid->column('card_limits', '卡种/单卡成功笔数限制/单卡时间限制(小时)')->display(function ($value) {
                if (!empty($value)) {
                    $card_limit = '';
                    foreach ($value as $limit) {
                        $card_limit .= $limit['cc_type'] . '&nbsp;/&nbsp;' . $limit['card_limit'] . '&nbsp;/&nbsp;' . $limit['card_limit_time'] . '<br />';
                    }
                    return $card_limit;
                } else {
                    return '无';
                }
            });
            $grid->column('remarks', '备注');
            $grid->column('sort', '排序');
            $grid->created_at;
            $grid->updated_at->sortable();
            $grid->column('config', '渠道配置')->display(function ($modal) use ($_this, $channelPid) {
                return $_this->build($channelPid, $this->id);
            });

            $grid->showCreateButton();
            $grid->showActions();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
			$grid->disableRowSelector();

            $grid->filter(function (Filter $filter) {
                $filter->equal('id')->width(4);
                $filter->equal('channel')->width(4);
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($this->status == ChannelModel::STATUS_ENABLE) {
                    Form::dialog('<b>路由列表</b>')
                        ->click('.channel-show-route')
                        ->width('1200px');
                    $url = '/admin/dcat-api/render?_current_=/admin/channel_pids/channel/routers' . '&channel_name=' . $this->channel . '&renderable=App_Admin_Actions_Grid_Channel_ChannelRouter';
                    $actions->append(PHP_EOL . "<span title='标识路由方案' style='color:#4c60a3' class='channel-show-route' data-url='{$url}'><i class='fa fa-link'></i></span>");
                }
            });
        });
	}

    /**
     * show form button by dialog
     *
     * @param [int] $channelPid
     * @param [int] $channelId
     * @return string
     */
	protected function build($channelPid, $channelId)
	{
		Form::dialog('<b>渠道配置</b>')
		    ->click('.channel-config-forms')
		    ->width('800px')
		    ->success('Dcat.reload()');
		$url = '/admin/dcat-api/render?_current_=/admin/channel_pids/'. $channelPid .'/channels?&key='. $channelPid .'&channel_id=' . $channelId . '&renderable=App_Admin_Actions_Grid_Channel_Config';
		return "<span style='color:#4c60a3' class='channel-config-forms' data-url='{$url}'><i class='fa fa-clone'></i>&nbsp;&nbsp;配置</span>";
    }
}
