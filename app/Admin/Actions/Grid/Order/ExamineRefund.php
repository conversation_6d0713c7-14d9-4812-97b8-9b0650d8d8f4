<?php

namespace App\Admin\Actions\Grid\Order;

use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Grid\RowAction;
use App\Admin\Forms\Order\ExamineRefund as FormsExamineRefund;

class ExamineRefund extends RowAction
{
    /**
     * @return string
     */
    protected $title = '审核';

    public function render()
    {
        // 实例化表单类并传递自定义参数
        $body = FormsExamineRefund::make()->payload(['id' => $this->getKey()]);

        return Modal::make()
            ->xl()
            ->title($this->title)
            ->body($body)
            ->button($this->title);
    }
}
