<?php

namespace App\Admin\Controllers\Channel;

use App\Admin\Actions\Grid\Channel\Channel;
use App\Admin\Repositories\ChannelPid;
use App\Models\Channel as ModelsChannel;
use App\Models\ChannelPid as ChannelPidModel;
use App\Models\ChannelSupplier;
use App\Models\DirectoryCc;
use App\Models\DirectoryChannelMainBody;
use App\Models\DirectoryCurrency;
use App\Models\Order;
use Dcat\Admin\Admin;
use Illuminate\Support\Carbon;
use App\Services\LimitCheckService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rule;

class PidController extends AdminController
{
	protected $title = 'PID管理';

	/**
	 * Make a grid builder.
	 *
	 * @return Grid
	 */
	protected function grid()
	{
		return Grid::make(new ChannelPid(['channelSupplier', 'channel:channel_pid_id,channel,url', 'directoryChannelMainBody']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
			$grid->id->sortable();
			$grid->channel_pid;
			$grid->column('channel_supplier.supplier_name');
			$grid->channel_type('渠道类型')->display(function ($value){
				return ChannelPidModel::$channelTypeMap[$value];
			});
			$grid->cc_types;
			$grid->payment_currency;
			$grid->settle_default_currency;
			$grid->settle_currencies;
			$grid->access_type->display(function ($value){
				return ChannelPidModel::$accessTypeMap[$value];
			});

			$grid->available_amount;
			$grid->column('day_statistics', '卡种/日交易金额')->display(function () {
                $nowTime     = Carbon::parse(date('Y-m-d H:i:s'));
                if ($this->channelSupplier->timezone != '' && validate_timezone($this->channelSupplier->timezone)){
                    // 生效日期
                    $effectiveDate = Carbon::parse($this->channelSupplier->effective_date);
                    if ($nowTime->gte($effectiveDate)){
                        $nowTime->setTimezone($this->channelSupplier->timezone);
                    }
                }

                $key                = 'Transaction_' . $this->channel_pid . '_Amount_' . $nowTime->format('Ym');  //缓存key
                $transactionChannel = Cache::has($key) ? Cache::get($key) : [];
				$statisticsMsg      = '';
				if (!isset($transactionChannel['day_amount'])) {
					$transactionChannel['day_amount'] = ['*' => 0.00];
				}

				foreach ($transactionChannel['day_amount'] as $ccType => $amount) {
					$statisticsMsg .= $ccType . ' / ' . amount_format($amount) . '<br>';
				}

				return $statisticsMsg;
            })->width('10%');

			$grid->column('month_statistics', '卡种/月交易金额')->display(function () {
                $nowTime     = Carbon::parse(date('Y-m-d H:i:s'));
                if ($this->channelSupplier->timezone != '' && validate_timezone($this->channelSupplier->timezone)){
                    // 生效日期
                    $effectiveDate = Carbon::parse($this->channelSupplier->effective_date);
                    if ($nowTime->gte($effectiveDate)){
                        $nowTime->setTimezone($this->channelSupplier->timezone);
                    }
                }

                $key                = 'Transaction_' . $this->channel_pid . '_Amount_' . $nowTime->format('Ym');  //缓存key
                $transactionChannel = Cache::has($key) ? Cache::get($key) : [];
				$statisticsMsg      = '';
				if (!isset($transactionChannel['month_amount'])) {
					$transactionChannel['month_amount'] = ['*' => 0.00];
				}

				foreach ($transactionChannel['month_amount'] as $ccType => $amount) {
					$statisticsMsg .= $ccType . ' / ' . amount_format($amount) . '<br>';
				}

				return $statisticsMsg;
            })->width('10%');

			$grid->column('day_astrict', '卡种/日限额')->display(function ($value) {
				$row = '无';
				if (!empty($value)) {
                    $astrict = '';
                    foreach ($value as $limit) {
                        $astrict .= $limit['cc_type'] . '&nbsp;/&nbsp;' . $limit['day_amount']. '<br />';
                    }

                    $row = $astrict;
                }

				return $row;
            });

            $grid->column('month_astrict', '卡种/月限额')->display(function ($value) {
				$row = '无';
				if (!empty($value)) {
                    $astrict = '';
                    foreach ($value as $limit) {
                        $astrict .= $limit['cc_type'] . '&nbsp;/&nbsp;' . $limit['month_amount']. '<br />';
                    }

                    $row = $astrict;
                }

				return $row;
            });
			$grid->column('directoryChannelMainBody.full_name', '主体名称');
			$grid->remarks;
			$grid->sort;
			$grid->created_at;
			$grid->updated_at->sortable();

            $grid->content('渠道配置')->display(function () {
                // 方案一：直接使用内联样式（简单快速）
                admin_style('.modal-xl { max-width: 1520px !important; }');

                // 方案二：使用自定义 CSS 文件（更系统化）
                // admin_css('/css/custom-modal.css');
                // admin_style('.modal-dialog.modal-xl { max-width: 1520px !important; }');

                return Modal::make()
                            ->xl()
                            ->body(Channel::make()->payload(['id' => $this->id]))
                            ->button('账单标识');
            });

			$grid->fixColumns(2, -2);
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableRowSelector();

			$grid->filter(function (Grid\Filter $filter) {
				$filter->equal('id');
                $filter->equal('channel_supplier_id')->select(ChannelSupplier::all()->pluck('supplier_name', 'id')->toArray())->load('channel_pid', '/channel_pids/get_pid/channel_pid');
				$filter->equal('channel_pid')->select(ChannelPidModel::pluck('channel_pid', 'channel_pid')->toArray());
				$filter->equal('main_bodys_id', '主体名称')->select(DirectoryChannelMainBody::get()->pluck('full_name', 'id')->toArray());
				$filter->equal('payment_currency')->select(DirectoryCurrency::get()->pluck('code', 'code')->toArray());
				$filter->where('cc_types', function ($query) {
					$ccTypes = $this->input ?? [];
					foreach ($ccTypes as $ccType) {
						$query->where('cc_types', 'like', '%' . $ccType . '%');
					}
				})->multipleSelect(DirectoryCc::get()->where('is_risk_control', DirectoryCc::IS_RISK_CONTROL_OPEN)->pluck('cc_type', 'cc_type')->toArray());
				$filter->equal('channel.channel', '账单标识')->select(ModelsChannel::get()->pluck('channel', 'channel')->toArray());
				$filter->equal('channel.url', '网址')->select(ModelsChannel::get()->pluck('url', 'url')->toArray());
			});
		});
	}

	/**
	 * Make a form builder.
	 *
	 * @return Form
	 */
	protected function form()
	{
		return Form::make(new ChannelPid('channel'), function (Form $form) {
			// 获取供应商数据
			$channelSupplierArr = ChannelSupplier::get()->pluck('supplier_name', 'id')->toArray();

			// 获取交易卡种
			$cctypeArr = DirectoryCc::get()->where('is_risk_control', DirectoryCc::IS_RISK_CONTROL_OPEN)->pluck('cc_type', 'cc_type')->toArray();

			// 获取交易币种
			$currencyArr = DirectoryCurrency::get()->pluck('code', 'code')->toArray();

			// 获取主体名称数据
			$mainBodyArr = DirectoryChannelMainBody::get()->pluck('full_name', 'id')->toArray();

			$form->display('id');

            $orderExists = false;
            if ($form->model()->channel && count($form->model()->channel)){
                $channel = $form->model()->channel->pluck('channel');
                $orderExists = Order::whereIn('channel', $channel)->exists();
            }

            if ($orderExists || !Admin::user()->inRoles(['PID Update','administrator'])) {
                $form->display('channel_pid');
                $form->select('channel_supplier_id')->options($channelSupplierArr)->disable();
                $form->select('channel_type')->options(ChannelPidModel::$channelTypeMap)->disable();
                $form->deleteInput(['channel_supplier_id', 'channel_type']);
            } else {
                $form->text('channel_pid')->rules(function ($form) {
                    return [
                        'required',
                        'string',
                        'max:32',
                        Rule::unique('channel_pids', 'channel_pid')->ignore($form->model()->id), // 重复性检测忽略自身
                    ];
                });

                $form->select('channel_supplier_id')->rules(['required', Rule::in(array_keys($channelSupplierArr))])->options($channelSupplierArr);
                $form->select('channel_type')->options(ChannelPidModel::$channelTypeMap)->rules(['required', Rule::in('0', '1')])->default('0');
            }

			$form->multipleSelect('cc_types')->options($cctypeArr)->rules('required|max:32')->saving(function ($value) {
				// 逗号拼接保存到数据库
				return implode(',', $value);
			});
			$form->select('payment_currency')->options($currencyArr)->default('');
			$form->select('settle_default_currency')->options($currencyArr)->rules('required|min:3|max:3');
			$form->multipleSelect('settle_currencies')->options($currencyArr)->rules('required|max:32')->saving(function ($value) {
				// 逗号拼接保存到数据库
				return implode(',', $value);
			});
			$form->select('access_type')->options(ChannelPidModel::$accessTypeMap)->rules(['required', Rule::in('s2s', 'directchannel')])->default('s2s');
			//卡种-日限额/卡种-月限额
			$cc_types = DirectoryCc::select('cc_type')->where('status', 1)->where('is_risk_control', DirectoryCc::IS_RISK_CONTROL_OPEN)->get()->pluck('cc_type', 'cc_type')->toArray() ?? [];
			if (!empty($cc_types)) {
				$cc_types = array_combine(array_values($cc_types), array_values($cc_types));
			}

			$form->table('day_astrict', function ($table) use ($cc_types) {
				$table->select('cc_type')->options(array_merge(['*' => '*'], $cc_types))->required();
				$table->text('day_amount')->required();
			});

			$form->table('month_astrict', function ($table) use ($cc_types) {
				$table->select('cc_type')->options(array_merge(['*' => '*'], $cc_types))->required();
				$table->text('month_amount')->required();
			});

			$form->text('sort')->default(0);
			$form->select('main_bodys_id')->options($mainBodyArr);
			$form->textarea('remarks');

			$form->display('created_at');
			$form->display('updated_at');

			$form->submitted(function (Form $form) {
				$dayAstrict = LimitCheckService::submitDataDuplicateCheck($form->day_astrict);
                if (!$dayAstrict['status']) {
                    return $form->response()->error('日限额规则重复！');
                }

				if (LimitCheckService::checkData($dayAstrict['data'], 'day_amount')) {
					return $form->response()->error('日限额不能小于0！');
				}

                $form->day_astrict = $dayAstrict['data'];

                $monthAstrict = LimitCheckService::submitDataDuplicateCheck($form->month_astrict);
                if (!$monthAstrict['status']) {
                    return $form->response()->error('月限额规则重复！');
                }

				if (LimitCheckService::checkData($monthAstrict['data'], 'month_amount')) {
					return $form->response()->error('月限额不能小于0！');
				}

				$mainBodyId = $form->main_bodys_id;
				if (empty($mainBodyId)) {
					$form->main_bodys_id = 0;
				}

                $form->month_astrict = $monthAstrict['data'];
			});

            $form->saving(function (Form $form) {
                // 如果是编辑
                if ($form->isEditing()) {
                    // 如果channel_supplier_id发生了变更
                    if ($form->channel_supplier_id && ($form->model()->channel_supplier_id != $form->channel_supplier_id)) {
                        $channelSupplierArr = ChannelSupplier::get()->pluck('supplier_name', 'id')->toArray();
                        foreach ($form->model()->channel as $channel){
                            $urlInfoList= explode('.',$channel->url);
                            $channel->channel = $channelSupplierArr[$form->channel_supplier_id] . '-' . $form->channel_pid . '-' . $urlInfoList[1];
                            $channel->save();
                        }
                    }
                }
            });
		});
	}

    //根据渠道ID返回对应PID
    protected function getPid($return = 'id')
    {
        $supplierId = $_GET['q'] ?? '';

        $select_id = $return == 'id' ? 'id as temp' : 'channel_pid as temp';

        $pidData = ChannelPidModel::where('channel_supplier_id', $supplierId)
            ->selectRaw("{$select_id}, channel_pid")->get()->toArray();

        $returnData = [];

        foreach ($pidData as $vo) {
            $returnData[] = [
                'id'   => $vo['temp'],
                'text' => $vo['channel_pid'],
            ];
        }

        return $returnData;
    }
}
