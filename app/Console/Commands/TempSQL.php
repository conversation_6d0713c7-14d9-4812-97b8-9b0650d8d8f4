<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TempSQL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:tempSQL';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fixed TempSQL command with proper MySQL syntax';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // 任务开始打印日志
        $this->info('tempSQL任务开始日志');

        try {
            // 修复方案：使用 Laravel 的 DB::table() 方法避免 SQL 语法错误
            $this->info('开始插入数据...');
            
            // 分批插入数据以避免内存问题
            $batchSize = 100;
            $totalInserted = 0;
            
            // 修复后的数据格式 - 所有单引号都已正确转义
            $data = [
                [
                    'mcc' => '1711',
                    'title' => 'Air Conditioning, Heating, and Plumbing Contractors',
                    'description' => 'Heating, plumbing and air conditioning services. Includes contractors that perform air system balancing and testing, drainage system installation, furnace repair, irrigation system installation, refrigeration and freezer work, sewer hookups and connections, solar heating, sprinkler system installation and water pump installation and servicing.',
                    'overall_risk_rating' => 'Low',
                    'aml_risk_level' => 'Low',
                    'fraud_risk_rating' => 'Low',
                    'card_scheme_risk_level' => 'Low',
                    'credit_risk_rating' => 'Low',
                    'remarks' => '',
                    'created_at' => '2025-05-23 08:02:15',
                    'updated_at' => '2025-05-23 08:02:15'
                ],
                [
                    'mcc' => '1731',
                    'title' => 'Electrical Contractors',
                    'description' => 'Electrical work such as fire alarm installation, sound equipment installation, telecommunications equipment installation, cable/internet installation, and telephone and telephone equipment installation.',
                    'overall_risk_rating' => 'Low',
                    'aml_risk_level' => 'Low',
                    'fraud_risk_rating' => 'Low',
                    'card_scheme_risk_level' => 'Low',
                    'credit_risk_rating' => 'Low',
                    'remarks' => '',
                    'created_at' => '2025-05-23 08:02:15',
                    'updated_at' => '2025-05-23 08:02:15'
                ],
                [
                    'mcc' => '5611',
                    'title' => 'Men\'s and Boy\'s Clothing and Accessories Stores',
                    'description' => 'Sale of men\'s and boys\' ready-to-wear clothing and accessories, including ties and hats.',
                    'overall_risk_rating' => 'Low',
                    'aml_risk_level' => 'Low',
                    'fraud_risk_rating' => 'Low',
                    'card_scheme_risk_level' => 'Low',
                    'credit_risk_rating' => 'Low',
                    'remarks' => '• Prohibited Activity: Dropshipping',
                    'created_at' => '2025-05-23 08:02:15',
                    'updated_at' => '2025-05-23 08:02:15'
                ],
                [
                    'mcc' => '8398',
                    'title' => 'Organizations, Charitable and Social Service',
                    'description' => 'Non-political fundraising organizations soliciting charitable donations/contributions on behalf of social welfare organizations. Also includes crowdfunding merchants that accept donations on behalf of individuals raising money for causes without receiving any financial benefit for doing so. Appropriate registration must be in place',
                    'overall_risk_rating' => 'Restricted',
                    'aml_risk_level' => 'High',
                    'fraud_risk_rating' => 'Low',
                    'card_scheme_risk_level' => 'Medium',
                    'credit_risk_rating' => 'Low',
                    'remarks' => '• Prohibited activity: Charity crowdfunding platforms',
                    'created_at' => '2025-05-23 08:02:15',
                    'updated_at' => '2025-05-23 08:02:15'
                ]
            ];
            
            // 批量插入
            foreach (array_chunk($data, $batchSize) as $batch) {
                DB::table('risk_mcc')->insert($batch);
                $totalInserted += count($batch);
                $this->info("已插入 {$totalInserted} 条记录");
            }
            
            $this->info("数据插入完成！总共插入了 {$totalInserted} 条记录");

        } catch (\Exception $e) {
            $this->error('SQL执行失败: ' . $e->getMessage());
            $this->error('错误文件: ' . $e->getFile());
            $this->error('错误行号: ' . $e->getLine());
            
            // 记录详细错误到日志
            Log::error('TempSQL Command Error', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        $this->info('tempSQL任务结束日志');
    }
}
