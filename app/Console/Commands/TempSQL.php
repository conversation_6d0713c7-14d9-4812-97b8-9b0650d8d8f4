<?php

namespace App\Console\Commands;

use App\Models\Channel;
use App\Models\ChannelSupplier;
use Illuminate\Console\Command;
use App\Services\ChannelPidLimitService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class TempMerchantAddBid extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:tempMerchantAddBid';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // 任务开始打印日志
        $this->info('addBid任务开始日志');

        try {
//            $a = '[{"cc_type": "*", "fail_limit": "3", "limit_time": "24"}]';
//            // 执行 sql 语句
//            $sql = "INSERT INTO merchant_businesses (business_id,merchant_id,merchant_name,open_blacklist,internal_status,fail_limits,created_at,updated_at) VALUES ('164791825957292009','164791825957292','kiosk',1,2,'$a',NOW(),NOW());";
//            // 直接执行原生 sql 语句
//            DB::insert($sql);
//
//            $sql = "INSERT INTO merchant_charge_rate_ccs (business_id,merchant_id,region,cc_type,transaction_rate,created_at,updated_at) VALUES ('164791825957292009','164791825957292','*','*',0.00,NOW(),NOW());";
//            // 直接执行原生 sql 语句
//            DB::insert($sql);

            $sql = "INSERT INTO merchant_urls
(merchant_id, merchant_name, business_id, url_name, main_url_name, website_mode, saas_url_name, d_mcc_id, d_brand_id, cc_types, url_status, pid_status, remarks, status_remarks, created_at, updated_at)
VALUES('164791825957292', 'kiosk', '164791825957292009', 'bus.com', 'bus.com', 0, '', NULL, NULL, NULL, 2, 0, NULL, NULL,NOW(),NOW());";
            // 直接执行原生 sql 语句
            DB::insert($sql);
        } catch (\Exception $e) {
            $this->info('addBid任务异常日志');
            $this->info($e->getMessage());
        }

        $this->info('addBid任务结束日志');
    }

}
