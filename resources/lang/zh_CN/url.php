<?php

use App\Models\MerchantUrl;
use App\Models\RiskCase;

return [
    'labels'  => [
        'revoke'             => '撤销',
        'Confirm'            => '确认执行撤销URL审核操作吗',
        'website_mode'       => ['自建站', '店匠', 'Shopyy'],
        'case'               => '卡组合规',
        'web'                => '网址信息',
        'audit_information'  => '审核信息',
        'g2'                 => 'G2',
        'location'           => '商户选址评估',
        'factors'            => '高风险业务因素',
        'disclosures'        => '网站信息披露',
        'tips'               => '提示',
        'tips_content'       => '当前选择的MCC Code与合规选择的MCC Code不一致，确要进行配置么？',
        'mcc_check_messages' => '如果不同意修改MCC Code，请选择与原始MCC Code一致的MCC'
    ],
    'fields'  => [
        'merchant_id'       => 'MID',
        'merchant_name'     => '商户名称',
        'business_id'       => 'BID',
        'url_name'          => '网址名称',
        'main_url_name'     => '主域名',
        'website_mode'      => '建站方式',
        'saas_url_name'     => 'SaaS网址名称',
        'd_mcc_id'          => 'MCC',
        'mcc.name'          => 'MCC',
        'd_brand_id'        => '网址品牌',
        'brand.name'        => '网址品牌',
        'cc_types'          => '交易卡种',
        'url_status'        => '状态',
        'remarks'           => '备注',
        'brand_name'        => '网址品牌',
        'files'             => '补充附件',
        'detail'            => '详情',
        'factors'           => [
            'cryptocurrency'              => '加密货币',
            'dropshipper'                 => '代发货（代发货/无库存电商卖家）',
            'pre_order'                   => '预售/预购',
            'startup'                     => '初创公司/创业企业',
            'cannabidiol'                 => '大麻二酚',
            'multi_level_marketing'       => '多层次营销/直销组织',
            'subscriptions_over_one_year' => '订阅服务超过1年',
            'buy_now_pay_later'           => '先买后付',
            'third_party_agents'          => '第三方代理',
        ],
        'disclosures'       => [
            'mcc'                      => 'MCC',
            'legal_entity_name'        => '法定实体名称',
            'governing_jurisdiction'   => '协议管辖地',
            'merchant_company_address' => '商户注册地址',
            'terms_and_conditions'     => '服务条款与条件',
            'privacy_policy'           => '隐私政策',
            'refund_return_policy'     => '退款/退货政策',
            'customer_service_contact' => '客服联系方式',
        ],
        'url_info'          => [
            'not_exist'         => 'URL不存在',
            'illegal'           => '非法操作',
            'status_immutable'  => '状态不可变',
            'incorrect_website' => '网址填写有误',
            'url_repeat'        => '主域名重复',
        ],
        'merchant'          => ['source' => '商户来源'],
        'case'              => ['merchant_id'   => '商户编号',
                                'case_number'   => '案例编号',
                                'case_type'     => '案例类型',
                                'auditor'       => '审核人员',
                                'user_id'       => '审核人员 id',
                                'completion_at' => '完成时间',
                                'created_at'    => '提交时间',
                                'riskCasesBid'  => 'BID'],
        'disclosuresByCase' => [
            'mcc' => '卡组合规所选MCC'
        ],
        'status_remarks'    => '商户可见备注',
        'urlCaseInfo'       => [
            'pid_status'                   => '审核结果',
            'audit_remark'                 => '内部备注',
            'status_remarks'               => '商户可见备注',
            'channel_report'               => 'G2报告',
            'merchant_location_assessment' => '该商户是否符合卡组织（所制定的）商户选址要求',
            'supplementary_annex'          => '补充附件',
            'audit_file'                   => '补充附件',
        ]
    ],
    'options' => [
        'url_status'   => MerchantUrl::$merchantStatusMap,
        'website_mode' => [
            '自建站' => '自建站',
            '店匠'   => '店匠',
            'Shopyy' => 'Shopyy',
        ],
        'check'        => [
            true  => '是',
            false => '否',
        ],
        'disclosure'   => [
            true  => '目前',
            false => '缺失',
        ],
        'audit'        => RiskCase::$auditResultSelectMap
    ],
];
